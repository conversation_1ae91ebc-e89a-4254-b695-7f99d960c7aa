/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #4FC3F7;
    --primary-dark: #29B6F6;
    --primary-light: #81D4FA;
    --secondary-color: #E1F5FE;
    --accent-color: #0277BD;
    --text-dark: #263238;
    --text-light: #546E7A;
    --white: #FFFFFF;
    --gradient: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    --gradient-soft: linear-gradient(135deg, rgba(79, 195, 247, 0.1) 0%, rgba(41, 182, 246, 0.05) 100%);
    --shadow: 0 10px 30px rgba(79, 195, 247, 0.15);
    --shadow-hover: 0 20px 60px rgba(79, 195, 247, 0.25);
    --success-color: #10B981;
    --whatsapp-color: #25D366;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.7;
    color: var(--text-dark);
    overflow-x: hidden;
    background: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    z-index: 1000;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(79, 195, 247, 0.1);
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand .logo {
    height: 50px;
    width: auto;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after,
.nav-menu a.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(79, 195, 247, 0.85) 0%,
        rgba(41, 182, 246, 0.7) 50%,
        rgba(38, 50, 56, 0.6) 100%
    );
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 3;
    color: white;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.hero-title {
    font-size: 4rem;
    font-weight: 900;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.highlight {
    color: var(--primary-color);
    font-weight: 900;
}

.hero-description {
    font-size: 1.3rem;
    line-height: 1.8;
    margin-bottom: 3rem;
    opacity: 0.95;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.stat {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    font-weight: 600;
    opacity: 0.9;
}

.hero-scroll {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
    color: white;
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Filter Section */
.filter-section {
    padding: 4rem 0;
    background: #F8FAFB;
    border-bottom: 1px solid rgba(79, 195, 247, 0.1);
}

.filter-wrapper {
    text-align: center;
}

.filter-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 3rem;
}

.filter-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    max-width: 900px;
    margin: 0 auto 2rem;
}

.filter-buttons .filter-btn:first-child {
    grid-column: 1 / -1;
    max-width: 200px;
    margin: 0 auto;
}

.filter-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    padding: 1.2rem 1.5rem;
    background: var(--white);
    border: 2px solid rgba(79, 195, 247, 0.2);
    border-radius: 20px;
    color: var(--text-dark);
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    min-height: 60px;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--gradient);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: var(--shadow);
}

.filter-btn i {
    font-size: 1.2rem;
}

/* Studies Section */
.studies-section {
    padding: 6rem 0;
    background: var(--white);
}

.studies-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.study-card {
    background: var(--white);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.4s ease;
    border: 1px solid rgba(79, 195, 247, 0.1);
    opacity: 1;
    transform: scale(1);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.study-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-hover);
    border-color: var(--primary-color);
}

.study-card.hidden {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

.study-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.study-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.study-card:hover .study-image img {
    transform: scale(1.1);
}

.study-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(79, 195, 247, 0.8) 0%,
        rgba(41, 182, 246, 0.6) 100%
    );
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    padding: 1.5rem;
}

.study-category {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.9);
    color: var(--primary-color);
    padding: 0.6rem 1.2rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.study-content {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.study-content h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
    line-height: 1.4;
}

.study-content p {
    color: var(--text-light);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.study-features {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    margin-bottom: 2rem;
}

.study-features span {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-size: 0.9rem;
    color: var(--text-dark);
    font-weight: 500;
}

.study-features i {
    color: var(--success-color);
    font-size: 1rem;
}

.study-footer {
    border-top: 1px solid rgba(79, 195, 247, 0.1);
    padding-top: 1.5rem;
    margin-top: auto;
}

.study-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.price-label {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 500;
}

.price-value {
    font-size: 1.1rem;
    color: var(--primary-color);
    font-weight: 700;
}

.study-actions {
    display: flex;
    gap: 1rem;
}

.btn-primary,
.btn-outline {
    flex: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.6rem;
    padding: 0.8rem 1.5rem;
    border-radius: 15px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    text-align: center;
}

.btn-primary {
    background: var(--gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 195, 247, 0.4);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

/* CTA Section */
.cta-section {
    padding: 6rem 0;
    background: var(--gradient);
    color: white;
}

.cta-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.cta-text h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
}

.cta-text p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.cta-actions {
    display: flex;
    gap: 1rem;
}

.btn-whatsapp {
    background: var(--whatsapp-color);
    color: white;
    padding: 1.2rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    border: 2px solid transparent;
}

.btn-whatsapp:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(37, 211, 102, 0.4);
}

.cta-actions .btn-primary {
    background: white;
    color: var(--primary-color);
    padding: 1.2rem 2.5rem;
    border-radius: 50px;
    font-size: 1.1rem;
}

.cta-actions .btn-primary:hover {
    background: #F8FAFB;
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: white;
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.footer-logo img {
    height: 40px;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1.5rem;
    color: white;
}

.footer-section p {
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(79, 195, 247, 0.4);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.8rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: rgba(255, 255, 255, 0.8);
}

.contact-item i {
    color: var(--primary-color);
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .studies-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .filter-buttons {
        grid-template-columns: 1fr;
        gap: 1rem;
        max-width: 300px;
    }

    .filter-buttons .filter-btn:first-child {
        grid-column: 1;
    }

    .filter-btn {
        width: 100%;
        padding: 1rem;
        font-size: 0.9rem;
    }

    .studies-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .cta-content {
        flex-direction: column;
        text-align: center;
    }

    .cta-actions {
        flex-direction: column;
        width: 100%;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .filter-title {
        font-size: 1.5rem;
    }

    .study-card {
        margin: 0 10px;
    }

    .study-content {
        padding: 1.5rem;
    }

    .study-actions {
        flex-direction: column;
        gap: 0.8rem;
    }
}
