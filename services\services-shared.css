/* Root Variables */
:root {
  --primary-color: #4fc3f7;
  --secondary-color: #29b6f6;
  --accent-color: #81c784;
  --success-color: #66bb6a;
  --text-dark: #2c3e50;
  --text-light: #7f8c8d;
  --white: #ffffff;
  --light-bg: #f8fafb;
  --gradient: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
  --shadow: 0 4px 20px rgba(79, 195, 247, 0.1);
  --shadow-hover: 0 8px 30px rgba(79, 195, 247, 0.2);
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Cairo", sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  background: var(--white);
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.highlight {
  background: var(--gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Header */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  border-bottom: 1px solid rgba(79, 195, 247, 0.1);
}

.navbar {
  padding: 1rem 0;
}

.navbar .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand .logo {
  height: 50px;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-menu a {
  text-decoration: none;
  color: var(--text-dark);
  font-weight: 600;
  transition: color 0.3s ease;
  position: relative;
}

.nav-menu a:hover,
.nav-menu a.active {
  color: var(--primary-color);
}

.nav-menu a.active::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--gradient);
  border-radius: 1px;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.nav-toggle span {
  width: 25px;
  height: 3px;
  background: var(--text-dark);
  margin: 3px 0;
  transition: 0.3s;
}

/* Hero Section */
.hero-section {
  position: relative;
  height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-top: 80px;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
}

.hero-background img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(79, 195, 247, 0.8) 0%,
    rgba(41, 182, 246, 0.6) 100%
  );
  z-index: -1;
}

.hero-content {
  text-align: center;
  color: white;
  max-width: 800px;
  animation: fadeInUp 1s ease;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.8rem 1.5rem;
  border-radius: 50px;
  backdrop-filter: blur(10px);
  margin-bottom: 2rem;
  font-weight: 600;
}

.hero-title {
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.hero-description {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.8;
}

/* Breadcrumb */
.breadcrumb-section {
  padding: 2rem 0;
  background: var(--light-bg);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-light);
}

.breadcrumb a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumb a:hover {
  color: var(--secondary-color);
}

.breadcrumb i {
  font-size: 0.8rem;
  color: var(--text-light);
}

/* Service Content Section */
.service-content {
  padding: 5rem 0;
  background: var(--white);
}

.service-layout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: start;
}

.service-main {
  background: var(--white);
  border-radius: 24px;
  padding: 4rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
}

.service-header {
  margin-bottom: 4rem;
  position: relative;
  z-index: 2;
}

.service-title {
  font-size: 2.8rem;
  font-weight: 800;
  color: var(--text-dark);
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.service-subtitle {
  font-size: 1.2rem;
  color: var(--text-light);
  line-height: 1.8;
  font-weight: 500;
}

.service-description {
  font-size: 1.1rem;
  line-height: 1.9;
  color: var(--text-dark);
  margin-bottom: 3rem;
}

.service-features {
  margin: 4rem 0;
  padding: 3rem 0;
  background: var(--white);
}

.features-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 3rem;
  text-align: center;
  position: relative;
  padding-bottom: 1rem;
}

.features-title i {
  color: var(--primary-color);
  font-size: 1.5rem;
  margin-left: 0.8rem;
}

.features-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2.5rem;
  margin-top: 2rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  padding: 0;
  background: transparent;
  border: none;
  transition: none;
  position: relative;
}

.feature-item::before {
  content: "";
  position: absolute;
  left: 35px;
  top: 70px;
  bottom: -40px;
  width: 2px;
  background: linear-gradient(to bottom, var(--primary-color), transparent);
  opacity: 0.3;
}

.feature-item:last-child::before {
  display: none;
}

.feature-icon {
  width: 70px;
  height: 70px;
  background: var(--white);
  border: 3px solid var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.5rem;
  flex-shrink: 0;
  position: relative;
  z-index: 2;
  box-shadow: 0 4px 20px rgba(79, 195, 247, 0.15);
}

.feature-content {
  flex: 1;
  padding-top: 0.5rem;
}

.feature-content h4 {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
  line-height: 1.3;
}

.feature-content p {
  color: var(--text-light);
  line-height: 1.8;
  font-size: 1rem;
}

/* Enhanced Feature Items Animation */
.feature-item {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.feature-item.animate {
  opacity: 1;
  transform: translateY(0);
}

/* Service Sidebar */
.service-sidebar {
  position: sticky;
  top: 120px;
}

.sidebar-card {
  background: var(--white);
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.06);
  margin-bottom: 2rem;
}

.sidebar-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  text-align: center;
  justify-content: center;
}

.sidebar-title i {
  color: var(--primary-color);
  font-size: 1.3rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1.2rem;
  padding: 1.2rem;
  background: rgba(79, 195, 247, 0.03);
  border-radius: 12px;
  transition: all 0.3s ease;
  text-align: right;
  direction: rtl;
}

.contact-item i {
  color: var(--primary-color);
  font-size: 1.4rem;
  width: 24px;
  flex-shrink: 0;
}

.contact-item span {
  color: var(--text-dark);
  font-weight: 600;
  font-size: 1rem;
}

.cta-card {
  background: var(--gradient);
  color: white;
  text-align: center;
}

.cta-card h3 {
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.cta-card p {
  margin-bottom: 1.5rem;
  opacity: 0.9;
  line-height: 1.6;
}

.btn-cta {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  text-decoration: none;
  border-radius: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-cta:hover {
  background: white;
  color: var(--primary-color);
  border-color: white;
  transform: translateY(-2px);
}

/* Buttons */
.btn-primary {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem 2rem;
  background: var(--gradient);
  color: white;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 195, 247, 0.4);
}

.btn-whatsapp {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem 2rem;
  background: #25d366;
  color: white;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
}

.btn-whatsapp:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
}

/* Footer */
.footer {
  background: var(--text-dark);
  color: white;
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 1.5rem;
  color: white;
}

.footer-section p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.footer-logo img {
  height: 40px;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-links a {
  width: 40px;
  height: 40px;
  background: rgba(79, 195, 247, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.8rem;
}

.footer-section ul li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: var(--primary-color);
}

.footer-contact-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: rgba(255, 255, 255, 0.8);
}

.footer-contact-item i {
  color: var(--primary-color);
  width: 20px;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .service-layout {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .service-sidebar {
    position: static;
    order: 1;
  }

  .service-main {
    order: 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .service-main {
    padding: 2.5rem;
    border-radius: 20px;
  }

  .service-title {
    font-size: 2.2rem;
  }

  .service-description {
    padding: 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .nav-toggle {
    display: flex;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .service-main {
    padding: 2rem;
    border-radius: 16px;
  }

  .service-title {
    font-size: 2rem;
  }

  .service-description {
    font-size: 0.95rem;
  }

  .features-title {
    font-size: 1.6rem;
  }

  .features-list {
    gap: 2rem;
  }

  .feature-item {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .feature-item::before {
    display: none;
  }

  .feature-icon {
    width: 60px;
    height: 60px;
    font-size: 1.3rem;
  }

  .feature-content h4 {
    font-size: 1.2rem;
  }

  .feature-content p {
    font-size: 0.95rem;
  }

  .sidebar-card {
    padding: 1.5rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 1.8rem;
  }

  .service-title {
    font-size: 1.8rem;
  }

  .features-title {
    font-size: 1.4rem;
  }

  .service-main {
    padding: 1.5rem;
    border-radius: 12px;
  }

  .service-title {
    font-size: 1.8rem;
  }

  .service-description {
    font-size: 0.9rem;
  }

  .features-list {
    gap: 1.5rem;
  }

  .feature-icon {
    width: 50px;
    height: 50px;
    font-size: 1.1rem;
  }

  .feature-content h4 {
    font-size: 1.1rem;
  }

  .feature-content p {
    font-size: 0.9rem;
  }

  .sidebar-card {
    padding: 1rem;
  }

  .contact-item {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .btn-primary,
  .btn-whatsapp,
  .btn-cta {
    padding: 0.8rem 1.5rem;
    font-size: 0.9rem;
  }
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.mb-1 {
  margin-bottom: 0.5rem;
}
.mb-2 {
  margin-bottom: 1rem;
}
.mb-3 {
  margin-bottom: 1.5rem;
}
.mb-4 {
  margin-bottom: 2rem;
}
.mb-5 {
  margin-bottom: 3rem;
}

.mt-1 {
  margin-top: 0.5rem;
}
.mt-2 {
  margin-top: 1rem;
}
.mt-3 {
  margin-top: 1.5rem;
}
.mt-4 {
  margin-top: 2rem;
}
.mt-5 {
  margin-top: 3rem;
}

.p-1 {
  padding: 0.5rem;
}
.p-2 {
  padding: 1rem;
}
.p-3 {
  padding: 1.5rem;
}
.p-4 {
  padding: 2rem;
}
.p-5 {
  padding: 3rem;
}

.d-flex {
  display: flex;
}
.align-items-center {
  align-items: center;
}
.justify-content-center {
  justify-content: center;
}
.gap-1 {
  gap: 0.5rem;
}
.gap-2 {
  gap: 1rem;
}
.gap-3 {
  gap: 1.5rem;
}
