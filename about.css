/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #4FC3F7;
    --primary-dark: #29B6F6;
    --primary-light: #81D4FA;
    --secondary-color: #E1F5FE;
    --accent-color: #0277BD;
    --text-dark: #263238;
    --text-light: #546E7A;
    --white: #FFFFFF;
    --gradient: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    --gradient-soft: linear-gradient(135deg, rgba(79, 195, 247, 0.1) 0%, rgba(41, 182, 246, 0.05) 100%);
    --shadow: 0 10px 30px rgba(79, 195, 247, 0.15);
    --shadow-hover: 0 20px 60px rgba(79, 195, 247, 0.25);
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.7;
    color: var(--text-dark);
    overflow-x: hidden;
    background: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    z-index: 1000;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(79, 195, 247, 0.1);
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand .logo {
    height: 50px;
    width: auto;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after,
.nav-menu a.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(79, 195, 247, 0.85) 0%,
        rgba(41, 182, 246, 0.7) 50%,
        rgba(38, 50, 56, 0.6) 100%
    );
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 3;
    color: white;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.hero-title {
    font-size: 4rem;
    font-weight: 900;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.highlight {
    color: var(--primary-color);
    font-weight: 900;
}

.hero-description {
    font-size: 1.3rem;
    line-height: 1.8;
    margin-bottom: 3rem;
    opacity: 0.95;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 3rem;
}

.stat {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 120px;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.hero-scroll {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
    color: white;
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Story Section */
.story-section {
    padding: 8rem 0;
    background: #F8FAFB;
    position: relative;
}

.story-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
    align-items: center;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--gradient-soft);
    color: var(--primary-color);
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(79, 195, 247, 0.2);
}

.story-title {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1.2;
    color: var(--text-dark);
    margin-bottom: 2rem;
}

.story-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

.story-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 2rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1rem;
    color: var(--text-dark);
    font-weight: 600;
}

.feature i {
    color: #10B981;
    font-size: 1.2rem;
}

.story-visual {
    position: relative;
}

.visual-card {
    border-radius: 24px;
    overflow: hidden;
    box-shadow: var(--shadow);
    position: relative;
}

.visual-card img {
    width: 100%;
    height: 500px;
    object-fit: cover;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.floating-card {
    position: absolute;
    background: var(--white);
    padding: 1rem 1.5rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-weight: 600;
    color: var(--text-dark);
    animation: float 3s ease-in-out infinite;
}

.floating-card:first-child {
    top: 20%;
    right: -10%;
    animation-delay: 0s;
}

.floating-card:last-child {
    bottom: 20%;
    left: -10%;
    animation-delay: 1.5s;
}

.floating-card i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Vision Mission Section */
.vision-mission-section {
    padding: 8rem 0;
    background: var(--white);
    position: relative;
}

.vm-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.vm-card {
    background: var(--white);
    padding: 3rem;
    border-radius: 24px;
    box-shadow: var(--shadow);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.vm-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient);
}

.vm-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.vm-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin-bottom: 2rem;
}

.vm-card h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.vm-card p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-light);
    margin-bottom: 2rem;
}

.vm-highlight {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    background: var(--gradient-soft);
    padding: 1rem 1.5rem;
    border-radius: 15px;
    font-weight: 600;
    color: var(--primary-color);
}

.vm-highlight i {
    font-size: 1.2rem;
}

/* Goals Section */
.goals-section {
    padding: 8rem 0;
    background: #F8FAFB;
}

.goals-header {
    text-align: center;
    margin-bottom: 5rem;
}

.goals-title {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1.2;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.goals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.goal-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 24px;
    box-shadow: var(--shadow);
    transition: all 0.4s ease;
    position: relative;
    border: 1px solid rgba(79, 195, 247, 0.1);
}

.goal-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-hover);
    border-color: var(--primary-color);
}

.goal-number {
    position: absolute;
    top: -15px;
    right: 2rem;
    background: var(--gradient);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.1rem;
}

.goal-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-soft);
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
}

.goal-card h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.goal-card p {
    color: var(--text-light);
    line-height: 1.7;
}

/* Services Overview Section */
.services-overview-section {
    padding: 8rem 0;
    background: var(--white);
}

.services-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
    align-items: center;
}

.services-visual {
    position: relative;
}

.services-visual img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    border-radius: 24px;
    box-shadow: var(--shadow);
}

.services-badge {
    position: absolute;
    top: 2rem;
    right: 2rem;
    background: var(--white);
    padding: 1rem 1.5rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-weight: 600;
    color: var(--primary-color);
}

.services-title {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1.2;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.services-description {
    font-size: 1.2rem;
    line-height: 1.8;
    color: var(--text-light);
    margin-bottom: 2rem;
}

.services-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.service-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.service-item:hover {
    transform: translateX(-5px);
    box-shadow: var(--shadow);
}

.service-item i {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-top: 0.2rem;
}

.service-item h4 {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.service-item p {
    color: var(--text-light);
    line-height: 1.6;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    background: var(--gradient);
    color: white;
    padding: 1.2rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: white;
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.footer-logo img {
    height: 40px;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1.5rem;
    color: white;
}

.footer-section p {
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(79, 195, 247, 0.4);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.8rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: rgba(255, 255, 255, 0.8);
}

.contact-item i {
    color: var(--primary-color);
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .story-wrapper,
    .services-wrapper,
    .vm-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
    
    .goals-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}
