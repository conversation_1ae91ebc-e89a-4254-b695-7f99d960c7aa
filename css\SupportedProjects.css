:root {
            --primary-color: #41AED9;
            --secondary-color: #41AED9;
            --accent-color: #e74c3c;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --text-color: #495057;
            --border-radius: 8px;
            --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            --transition: all 0.3s ease-in-out;
        }
        
        body {
            font-family: 'Ta<PERSON><PERSON>', sans-serif;
            color: var(--text-color);
            background-color: #f9f9f9;
            line-height: 1.6;
        }
        
        
        /* Hero Section */
        .service-hero {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.4) 100%),
                        url('../images/127118.jpg') center/cover no-repeat;
            height: 50vh;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
        }
        
        .service-hero h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 20px;
            animation: fadeInDown 1s ease;
        }
        
        /* Content Section */
        .service-content {
           
            padding: 80px 0;
            background-color: white;
        }
        
        /* Filter Section */
        .filter-section {
            background-color: #f8fafc;
            padding: 30px 0;
            margin-bottom: 40px;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }
        
        .filter-btn {
            margin: 5px;
            border-radius: 50px;
            padding: 8px 20px;
            transition: var(--transition);
        }
        
        .filter-btn.active, .filter-btn:hover {
            background-color: var(--secondary-color);
            color: white;
        }
        
        /* Bank Cards */
        .bank-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
            transition: var(--transition);
            width: 390px;
            margin: 10px;
            margin-bottom: 60px;
            padding: 70px;
            height: 100%;
            display: none; /* Initially hide all cards */
        }
        
        .bank-card.visible {
            display: block; /* Show cards with visible class */
        }
        
        .bank-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }
        
        .bank-image {
            height: 200px;
            overflow: hidden;
        }
        
        .bank-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }
        
        .bank-card:hover .bank-image img {
            transform: scale(1.05);
        }
        
        .bank-body {
            padding: 20px;
        }
        
        .bank-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary-color);
        }
        
        .show-more-btn {
            margin: 30px auto;
            display: block;
            width: 200px;
        }
        
        /* Responsive Adjustments */
        @media (max-width: 992px) {
            .service-hero {
                height: 45vh;
                min-height: 350px;
            }
            
            .service-hero h1 {
                font-size: 2.5rem;
            }
        }
        
        @media (max-width: 768px) {
            .service-hero {
                height: 40vh;
                min-height: 300px;
            }
            
            .service-hero h1 {
                font-size: 2rem;
            }
        }
        
        @media (max-width: 576px) {
            .service-hero {
                height: 35vh;
                min-height: 250px;
            }
            
            .service-hero h1 {
                font-size: 1.8rem;
            }
        }
        