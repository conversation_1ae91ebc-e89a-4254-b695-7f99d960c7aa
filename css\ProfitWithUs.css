
        :root {
            --primary-color: #41AED9;
            --secondary-color: #41AED9;
            --accent-color: #e74c3c;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --text-color: #495057;
            --border-radius: 8px;
            --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            --transition: all 0.3s ease-in-out;
        }
        
        body {
            font-family: '<PERSON><PERSON><PERSON>', sans-serif;
            color: var(--text-color);
            background-color: #f9f9f9;
            line-height: 1.6;
        }
        /* Hero Section */
        .profit-hero {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.4) 100%), 
                        url('/images/business-concept-with-graphic-holography.jpg') center/cover no-repeat;
            height: 60vh;
            min-height: 450px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
        }
        
        .profit-hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            animation: fadeInDown 1s ease;
        }
        
        .profit-hero p {
            font-size: 1.3rem;
            margin-bottom: 30px;
            max-width: 700px;
            margin-right: auto;
            margin-left: auto;
            animation: fadeInUp 1s ease 0.3s both;
        }
        
        /* About Section */
        .about-profit {
            padding: 80px 0;
            background-color: white;
        }
        
        /* Updated Card Styles */
.profit-card {
    background-color: white;
    border-radius: 12px;
    padding: 30px;
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.profit-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: all 0.3s ease;
}

.profit-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.08);
}

.profit-card:hover::before {
    height: 6px;
}

.profit-card h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: 600;
    position: relative;
    padding-bottom: 15px;
}

.profit-card h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 3px;
}

.profit-card p {
    color: #555;
    line-height: 1.7;
}

.profit-card ul {
    list-style: none;
    padding: 0;
}

.profit-card ul li {
    position: relative;
    padding-right: 25px;
    margin-bottom: 10px;
    color: #555;
}

.profit-card ul li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--primary-color);
    font-weight: bold;
}
        
        /* Form Section */
        .profit-form-section {
            padding: 80px 0;
            background-color: #f8f9fa;
        }
        
       /* Updated Form Styles */
.form-card {
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
}

.form-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.form-container {
    padding: 40px;
}

.form-floating {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-control {
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
    height: auto;
    background-color: #f9f9f9;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
    background-color: white;
}

.form-label {
    position: absolute;
    right: 15px;
    top: 15px;
    color: #6c757d;
    transition: all 0.3s ease;
    pointer-events: none;
    background-color: transparent;
}

.form-control:focus ~ .form-label,
.form-control:not(:placeholder-shown) ~ .form-label {
    top: -8px;
    right: 10px;
    font-size: 12px;
    color: var(--primary-color);
    background-color: white;
    padding: 0 5px;
}

textarea.form-control {
    min-height: 120px;
}

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.profit-card {
    animation: fadeIn 0.6s ease forwards;
}

.profit-card:nth-child(2) {
    animation-delay: 0.2s;
}

.profit-card:nth-child(3) {
    animation-delay: 0.4s;
} 
        
        /* Responsive Adjustments */
        @media (max-width: 992px) {
            .profit-hero {
                height: 50vh;
                min-height: 350px;
            }
            
            .profit-hero h1 {
                font-size: 2.8rem;
            }
            
            .profit-hero p {
                font-size: 1.1rem;
            }
        }
        
        @media (max-width: 768px) {
            .profit-hero {
                height: 45vh;
                min-height: 300px;
            }
            
            .profit-hero h1 {
                font-size: 2.3rem;
            }
            
            .form-container {
                padding: 30px;
            }
        }
        
        @media (max-width: 576px) {
            .profit-hero {
                height: 40vh;
                min-height: 250px;
            }
            
            .profit-hero h1 {
                font-size: 2rem;
            }
            
            .profit-hero p {
                font-size: 1rem;
            }
            
            .whatsapp-float {
                width: 50px;
                height: 50px;
                font-size: 25px;
                bottom: 20px;
                left: 20px;
            }
        }