 :root {
            --primary-color: #41AED9;
            --secondary-color: #000000;
            --accent-color: #25d366;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --text-color: #495057;
        }
        
        body {
            font-family: '<PERSON><PERSON><PERSON>', sans-serif;
            color: var(--text-color);
            background-color: #ffffff;
            overflow-x: hidden;
        }
        
         
        /* Updated Navbar Styles */
        .navbar {
            background-color: white !important;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            font-size:smaller;
            
            
            
        }
        
        
        .navbar.scrolled {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }
        
        .nav-link {
            color: var(--dark-color) !important;
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            position: relative;
            
        }
        .nav-item {
            margin: 7px;
        }
        .nav-link:hover,
        .nav-link:focus,
        .nav-link.active {
            color: var(--primary-color) !important;
        }
        
        .nav-link:after {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 0;
            height: 2px;
            transition: width 0.3s ease;
        }
        
        .nav-link:hover:after,
        .nav-link.active:after {
            width: 100%;
            right: auto;
            left: 0;
        }
        
        .dropdown-menu {
            background-color: white;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .dropdown-item {
            color: var(--dark-color);
            transition: all 0.2s;
            padding: 0.5rem 1.5rem;
            font-size:smaller;
        }
        
        .dropdown-item:hover,
        .dropdown-item:focus {
            background-color: #f8f9fa;
            color: var(--primary-color);
            padding-right: 2rem;
        }
        /* Navbar dropdown scroll fix */
.navbar .dropdown-menu {
    max-height: 70vh; /* 70% of viewport height */
    overflow-y: auto;
    overscroll-behavior: contain; /* prevents scroll chaining */
}

/* Optional: Style the scrollbar */
.navbar .dropdown-menu::-webkit-scrollbar {
    width: 8px;
}

.navbar .dropdown-menu::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.navbar .dropdown-menu::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

.navbar .dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #3a9cc8;
}
        
        .navbar-toggler {
            border: none;
            padding: 0.5rem;
        }
        
        .navbar-toggler:focus {
            box-shadow: none;
        }
        
        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
            transition: all 0.3s;
        }
        
        .navbar-toggler:hover .navbar-toggler-icon {
            transform: rotate(90deg);
        }
        
        
        
      
        
        /* Buttons */
        .btn {
            font-weight: 500;
            padding: 0.6rem 1.5rem;
            border-radius: 50px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
        .btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            z-index: -1;
        }
        
        .btn:hover:before {
            width: 100%;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #3a9cc8;
            border-color: #3a9cc8;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(65, 174, 217, 0.3);
        }
        
        .btn-whatsapp {
            background-color: var(--accent-color);
            color: white;
        }
        
        .btn-whatsapp:hover {
            background-color: #1da851;
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(37, 211, 102, 0.3);
        }
        
        .btn-outline-primary {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(65, 174, 217, 0.3);
        }
        
        /* Footer */
        footer {
            background-color: var(--secondary-color);
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        footer:before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(65, 174, 217, 0.1) 0%, rgba(0, 0, 0, 0.9) 100%);
            z-index: 0;
        }
        
        .footer-links a {
            color: #adb5bd;
            text-decoration: none;
            transition: all 0.3s;
            position: relative;
            display: inline-block;
        }
        
        .footer-links a:after {
            content: '';
            position: absolute;
            bottom: -2px;
            right: 0;
            width: 0;
            height: 1px;
            background-color: var(--primary-color);
            transition: width 0.3s ease;
        }
        
        .footer-links a:hover {
            color: var(--primary-color);
            transform: translateX(-5px);
        }
        
        .footer-links a:hover:after {
            width: 100%;
            right: auto;
            left: 0;
        }
        
        .social-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            color: white;
        }
        
        .social-icon:hover {
            background-color: var(--primary-color);
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 5px 15px rgba(65, 174, 217, 0.3);
        }
        
        .payment-methods img {
            height: 30px;
            margin-left: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
            filter: grayscale(100%);
            opacity: 0.7;
        }
        
        .payment-methods img:hover {
            filter: grayscale(0%);
            opacity: 1;
            transform: translateY(-3px);
        }
        
        /* Contact Form */
        .contact-form {
            background-color: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        
        
        /* WhatsApp Float Button */
        .whatsapp-float {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background-color: var(--accent-color);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-align: center;
            font-size: 30px;
            box-shadow: 0 10px 25px rgba(37, 211, 102, 0.3);
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            animation: pulse 2s infinite;
        }
        
        .whatsapp-float:hover {
            transform: scale(1.1) rotate(10deg);
            color: white;
            box-shadow: 0 15px 30px rgba(37, 211, 102, 0.4);
            animation: none;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7); }
            70% { box-shadow: 0 0 0 15px rgba(37, 211, 102, 0); }
            100% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0); }
        }
        
        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .hero-section {
                padding: 100px 0;
            }
            
            .hero-section h1 {
                font-size: 2.5rem;
            }
            
            .section-title {
                font-size: 1.8rem;
            }
            
            .whatsapp-float {
                width: 50px;
                height: 50px;
                font-size: 24px;
                bottom: 20px;
                left: 20px;
            }
            
            .feature-box,
            .service-card,
            .counter-card {
                margin-bottom: 20px;
            }
            
            .testimonial-box {
                margin: 0 5px;
            }
            
            .client-img-container {
                width: 60px;
                height: 60px;
            }
        }
        
        /* Animation Classes */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }
        
        .animate-on-scroll.animated {
            opacity: 1;
            transform: translateY(0);
        }
        
        .delay-1 {
            transition-delay: 0.2s;
        }
        
        .delay-2 {
            transition-delay: 0.4s;
        }
        
        .delay-3 {
            transition-delay: 0.6s;
        }

       /* Buttons */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #3a9cc8;
            border-color: #3a9cc8;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(65, 174, 217, 0.3);
        }
        
        .btn-outline-primary {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        /* WhatsApp Float Button */
        .whatsapp-float {
            position: fixed;
            bottom: 30px;
            left: 30px;
            width: 60px;
            height: 60px;
            background-color: #25d366;
            color: white;
            border-radius: 50%;
            text-align: center;
            font-size: 30px;
            box-shadow: 0 5px 15px rgba(37, 211, 102, 0.3);
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
        }
        
        .whatsapp-float:hover {
            background-color: #128c7e;
            color: white;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
        }
        
        /* Text Justify */
        .text-justify {
            text-align: justify;
        }
        
        /* Responsive Adjustments */
        @media (max-width: 992px) {
            .service-hero {
                height: 45vh;
                min-height: 350px;
            }
            
            .service-hero h1 {
                font-size: 2.5rem;
            }
        }
        
        @media (max-width: 768px) {
            .service-hero {
                height: 40vh;
                min-height: 300px;
            }
            
            .service-hero h1 {
                font-size: 2rem;
            }
        }
        
        @media (max-width: 576px) {
            .service-hero {
                height: 35vh;
                min-height: 250px;
            }
            
            .service-hero h1 {
                font-size: 1.8rem;
            }
        }
        
        /* Animation Classes */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }
        
        .animate-on-scroll.animated {
            opacity: 1;
            transform: translateY(0);
        }
        
        .delay-1 {
            transition-delay: 0.2s;
        }
        
        .delay-2 {
            transition-delay: 0.4s;
        }
        
        .delay-3 {
            transition-delay: 0.6s;
        }
        
        /* Payment Methods */
        .payment-methods img {
            height: 30px;
            margin: 0 5px;
            filter: grayscale(100%);
            transition: var(--transition);
        }
        
        .payment-methods img:hover {
            filter: grayscale(0%);
        }