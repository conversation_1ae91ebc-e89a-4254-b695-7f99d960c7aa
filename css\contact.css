
        /* Hero Section */
        .contact-hero {
            background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('/images/arab-man-works-efficiently-browsing-internet-takes-notes-communicates-digitally-his-modern-office-young-muslim-guy-doing-research-writing-his-notebook.jpg');
            background-size: cover;
            background-position: center;
            height: 50vh;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
        }
        
        .contact-hero-content {
            max-width: 800px;
            padding: 0 20px;
        }
        
        .contact-hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
            animation: fadeInDown 1s ease;
        }
        
        .contact-hero p {
            font-size: 1.25rem;
            margin-bottom: 30px;
            text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.5);
            animation: fadeInUp 1s ease 0.3s both;
        }
        
        /* Contact Section */
        .contact-section {
            padding: 80px 0;
            background-color: #ffffff;
        }
        
        .contact-form {
            background-color: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .contact-form:hover {
            transform: translateY(-5px);
        }
        
        /* Updated Contact Info Box (without hover effects) */
.contact-info-box {
    background-color: var(--light-color);
    border-radius: 15px;
    padding: 30px;
    margin: 10px;
    height: 100%;
    position: relative;
    overflow: hidden;
    z-index: 1;
    border-left: 4px solid var(--primary-color);
}

.contact-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

        
        .section-title {
            position: relative;
            margin-bottom: 30px;
            color: var(--secondary-color);
            font-weight: 700;
        }
        
        .section-title:after {
            content: '';
            position: absolute;
            bottom: -15px;
            right: 0;
            width: 80px;
            height: 3px;
            background: var(--primary-color);
            animation: lineGrow 1s ease-out;
        }
        
        @keyframes lineGrow {
            from { width: 0; }
            to { width: 80px; }
        }
        
        /* Form Styles */
        .form-control {
            border-radius: 8px;
            padding: 12px 15px;
            border: 1px solid #ddd;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(65, 174, 217, 0.25);
        }
        
        .form-label {
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .form-select {
            padding: 12px 15px;
            border-radius: 8px;
        }
        
        /* Buttons */
        .btn {
            font-weight: 500;
            padding: 0.6rem 1.5rem;
            border-radius: 50px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
        .btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            z-index: -1;
        }
        
        .btn:hover:before {
            width: 100%;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #3a9cc8;
            border-color: #3a9cc8;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(65, 174, 217, 0.3);
        }
        
        /* Map Section */
        .map-section {
            padding: 60px 0;
            background-color: var(--light-color);
        }
        
        .map-container {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            height: 400px;
        }
        
        #branches {
    background-color: #f8f9fa;
    padding: 80px 0;
}

#branches .section-title {
    color: var(--secondary-color);
}

#branches .contact-info-box {
    background-color: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

#branches .contact-icon {
    background-color: rgba(65, 174, 217, 0.1);
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

        /* Animation Classes */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }
        
        .animate-on-scroll.animated {
            opacity: 1;
            transform: translateY(0);
        }
        
        .delay-1 {
            transition-delay: 0.2s;
        }
        
        .delay-2 {
            transition-delay: 0.4s;
        }
        
        .delay-3 {
            transition-delay: 0.6s;
        }
        
        /* Responsive Adjustments */
        @media (max-width: 992px) {
            .contact-hero h1 {
                font-size: 2.5rem;
            }
        }
        
        @media (max-width: 768px) {
            .contact-hero {
                height: 40vh;
                min-height: 350px;
            }
            
            .contact-hero h1 {
                font-size: 2rem;
            }
            
            .contact-hero p {
                font-size: 1rem;
            }
            
            .contact-form {
                padding: 30px 20px;
            }
        }
        
        @media (max-width: 576px) {
            .contact-hero {
                height: 35vh;
                min-height: 300px;
            }
        }