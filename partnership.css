/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #4FC3F7;
    --primary-dark: #29B6F6;
    --primary-light: #81D4FA;
    --secondary-color: #E1F5FE;
    --accent-color: #0277BD;
    --text-dark: #263238;
    --text-light: #546E7A;
    --white: #FFFFFF;
    --gradient: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    --gradient-soft: linear-gradient(135deg, rgba(79, 195, 247, 0.1) 0%, rgba(41, 182, 246, 0.05) 100%);
    --shadow: 0 10px 30px rgba(79, 195, 247, 0.15);
    --shadow-hover: 0 20px 60px rgba(79, 195, 247, 0.25);
    --success-color: #10B981;
    --warning-color: #F59E0B;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.7;
    color: var(--text-dark);
    overflow-x: hidden;
    background: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    z-index: 1000;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(79, 195, 247, 0.1);
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand .logo {
    height: 50px;
    width: auto;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after,
.nav-menu a.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(79, 195, 247, 0.85) 0%,
        rgba(41, 182, 246, 0.7) 50%,
        rgba(38, 50, 56, 0.6) 100%
    );
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 3;
    color: white;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.hero-title {
    font-size: 4rem;
    font-weight: 900;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.highlight {
    color: var(--primary-color);
    font-weight: 900;
}

.hero-description {
    font-size: 1.3rem;
    line-height: 1.8;
    margin-bottom: 3rem;
    opacity: 0.95;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-cta {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.btn-primary,
.btn-outline {
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1.2rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background: var(--gradient);
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.btn-outline {
    background: transparent;
    color: white;
    border-color: rgba(255, 255, 255, 0.5);
}

.btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
}

.hero-scroll {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
    color: white;
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Intro Section */
.intro-section {
    padding: 8rem 0;
    background: #F8FAFB;
    position: relative;
}

.intro-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
    align-items: center;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--gradient-soft);
    color: var(--primary-color);
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(79, 195, 247, 0.2);
}

.intro-title {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1.2;
    color: var(--text-dark);
    margin-bottom: 2rem;
}

.intro-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-light);
    margin-bottom: 2rem;
}

.intro-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1rem;
    color: var(--text-dark);
    font-weight: 600;
}

.feature i {
    color: var(--success-color);
    font-size: 1.2rem;
}

.intro-visual {
    position: relative;
}

.visual-card {
    border-radius: 24px;
    overflow: hidden;
    box-shadow: var(--shadow);
    position: relative;
}

.visual-card img {
    width: 100%;
    height: 500px;
    object-fit: cover;
}

.floating-stats {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.stat-card {
    position: absolute;
    background: var(--white);
    padding: 1.5rem;
    border-radius: 20px;
    box-shadow: var(--shadow);
    text-align: center;
    animation: float 3s ease-in-out infinite;
}

.stat-card:first-child {
    top: 20%;
    right: -10%;
    animation-delay: 0s;
}

.stat-card:last-child {
    bottom: 20%;
    left: -10%;
    animation-delay: 1.5s;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 900;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 600;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Benefits Section */
.benefits-section {
    padding: 8rem 0;
    background: var(--white);
}

.benefits-header {
    text-align: center;
    margin-bottom: 5rem;
}

.benefits-title {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1.2;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.benefits-description {
    font-size: 1.2rem;
    color: var(--text-light);
    line-height: 1.8;
    max-width: 600px;
    margin: 0 auto;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.benefit-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 24px;
    box-shadow: var(--shadow);
    transition: all 0.4s ease;
    position: relative;
    border: 1px solid rgba(79, 195, 247, 0.1);
    text-align: center;
}

.benefit-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-hover);
    border-color: var(--primary-color);
}

.benefit-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin: 0 auto 1.5rem;
}

.benefit-card h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.benefit-card p {
    color: var(--text-light);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.benefit-highlight {
    background: var(--gradient-soft);
    color: var(--primary-color);
    padding: 0.8rem 1.5rem;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.9rem;
}

/* How It Works Section */
.how-it-works-section {
    padding: 8rem 0;
    background: #F8FAFB;
}

.how-header {
    text-align: center;
    margin-bottom: 5rem;
}

.how-title {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1.2;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.steps-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.step-item {
    position: relative;
    text-align: center;
}

.step-number {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 50px;
    background: var(--gradient);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 900;
    font-size: 1.2rem;
    z-index: 2;
}

.step-content {
    background: var(--white);
    padding: 3rem 2rem 2rem;
    border-radius: 24px;
    box-shadow: var(--shadow);
    transition: all 0.4s ease;
    margin-top: 25px;
}

.step-content:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.step-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-soft);
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.8rem;
    margin: 0 auto 1.5rem;
}

.step-content h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.step-content p {
    color: var(--text-light);
    line-height: 1.7;
}

/* Form Section */
.form-section {
    padding: 8rem 0;
    background: var(--white);
}

.form-wrapper {
    max-width: 800px;
    margin: 0 auto;
}

.form-header {
    text-align: center;
    margin-bottom: 4rem;
}

.form-title {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1.2;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.form-description {
    font-size: 1.2rem;
    color: var(--text-light);
    line-height: 1.8;
}

.partnership-form {
    background: #F8FAFB;
    padding: 3rem;
    border-radius: 24px;
    box-shadow: var(--shadow);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.form-group {
    position: relative;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.8rem;
    font-size: 1rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid rgba(79, 195, 247, 0.2);
    border-radius: 15px;
    font-size: 1rem;
    font-family: inherit;
    transition: all 0.3s ease;
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.1);
}

.form-group i {
    position: absolute;
    top: 50%;
    right: 1rem;
    transform: translateY(-50%);
    color: var(--primary-color);
    font-size: 1.1rem;
}

.form-group textarea + i {
    top: 3rem;
    transform: none;
}

.form-actions {
    text-align: center;
    margin-top: 2rem;
}

.submit-btn {
    background: var(--gradient);
    color: white;
    padding: 1.2rem 3rem;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    box-shadow: var(--shadow);
}

.submit-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

/* Contact Section */
.contact-section {
    padding: 8rem 0;
    background: #F8FAFB;
}

.contact-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.contact-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.contact-info p {
    color: var(--text-light);
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 2rem;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.contact-method:hover {
    transform: translateX(-5px);
    box-shadow: var(--shadow);
}

.method-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.method-info h4 {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.3rem;
}

.method-info p {
    color: var(--text-light);
    margin: 0;
    font-size: 0.95rem;
}

.contact-visual img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 24px;
    box-shadow: var(--shadow);
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: white;
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.footer-logo img {
    height: 40px;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1.5rem;
    color: white;
}

.footer-section p {
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(79, 195, 247, 0.4);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.8rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: rgba(255, 255, 255, 0.8);
}

.contact-item i {
    color: var(--primary-color);
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        right: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: right 0.3s ease;
        z-index: 999;
    }

    .nav-menu.active {
        right: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .nav-menu a {
        font-size: 1.2rem;
        padding: 1rem;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }

    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .intro-wrapper,
    .contact-wrapper {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .benefits-grid,
    .steps-container {
        grid-template-columns: 1fr;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}
