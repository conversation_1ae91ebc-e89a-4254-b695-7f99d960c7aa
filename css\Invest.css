
        /* Page Header */
        .page-header {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.4) 100%), url('/images/2149151173.jpg') center/cover no-repeat;
           height: 60vh;
            min-height: 450px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
        }
        
        /* Filter Buttons */
        .filter-buttons {
            margin-bottom: 30px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .filter-btn {
            border: 2px solid var(--primary-color);
            background: transparent;
            color: var(--primary-color);
            padding: 8px 20px;
            border-radius: 30px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .filter-btn:hover,
        .filter-btn.active {
            background: var(--primary-color);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(65, 174, 217, 0.3);
        }
        
        /* Project Cards */
        .project-card {
            border: none;
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            height: 100%;
            margin-bottom: 30px;
        }
        
        .project-card img {
            transition: all 0.5s ease;
            height: 250px;
            object-fit: cover;
            width: 100%;
        }
        
        .project-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .project-card:hover img {
            transform: scale(1.05);
        }
        
        .project-card .card-body {
            padding: 20px;
        }
        
        .project-card .card-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--secondary-color);
            transition: all 0.3s ease;
        }
        
        .project-card:hover .card-title {
            color: var(--primary-color);
        }
        
        .project-card .btn {
            border-radius: 30px;
            padding: 8px 20px;
            font-weight: 500;
        }
        
        
        
        /* Section Title */
        .section-title {
            position: relative;
            margin-bottom: 40px;
            color: var(--secondary-color);
            font-weight: 700;
            text-align: center;
        }
        
        .section-title:after {
            content: '';
            position: absolute;
            bottom: -15px;
            right: 50%;
            transform: translateX(50%);
            width: 80px;
            height: 3px;
            background: var(--primary-color);
        }
        
        
        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .page-header {
                height: 250px;
            }
            
            .page-header h1 {
                font-size: 2rem;
            }
            
            .filter-buttons {
                gap: 8px;
            }
            
            .filter-btn {
                padding: 6px 15px;
                font-size: 0.9rem;
            }
        }
        
        @media (max-width: 576px) {
            .page-header {
                height: 200px;
            }
            
            .page-header h1 {
                font-size: 1.8rem;
            }
            
            .filter-buttons {
                gap: 5px;
            }
        }