/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #4FC3F7;
    --primary-dark: #29B6F6;
    --primary-light: #81D4FA;
    --secondary-color: #E1F5FE;
    --accent-color: #0277BD;
    --text-dark: #263238;
    --text-light: #546E7A;
    --white: #FFFFFF;
    --gradient: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    --shadow: 0 10px 30px rgba(79, 195, 247, 0.2);
    --shadow-hover: 0 15px 40px rgba(79, 195, 247, 0.3);
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    height: 50px;
    width: auto;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Carousel Section */
.hero-carousel {
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.carousel-container {
    position: relative;
    width: 100%;
    height: 100vh;
}

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translateX(100%);
}

.carousel-slide.active {
    opacity: 1;
    transform: translateX(0);
}

.carousel-slide.prev {
    transform: translateX(-100%);
}

.slide-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.slide-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.6) 0%,
        rgba(79, 195, 247, 0.3) 50%,
        rgba(0, 0, 0, 0.4) 100%
    );
    z-index: 2;
}

.slide-content {
    position: relative;
    z-index: 3;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    height: 100vh;
    max-width: 600px;
    color: white;
    text-align: right;
}

.slide-title {
    font-size: 3.5rem;
    font-weight: 900;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.slide-title .highlight {
    background: linear-gradient(135deg, #4FC3F7 0%, #81D4FA 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
}

.slide-subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2.5rem;
    line-height: 1.8;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.slide-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Carousel Navigation */
.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 4;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 2rem;
    pointer-events: none;
}

.carousel-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: all;
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-btn:hover {
    background: rgba(79, 195, 247, 0.8);
    border-color: rgba(79, 195, 247, 1);
    transform: scale(1.1);
    box-shadow: 0 8px 24px rgba(79, 195, 247, 0.4);
}

/* Carousel Indicators */
.carousel-indicators {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 4;
    display: flex;
    gap: 1rem;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.6);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: #4FC3F7;
    border-color: #4FC3F7;
    transform: scale(1.2);
    box-shadow: 0 0 12px rgba(79, 195, 247, 0.6);
}

.indicator:hover {
    background: rgba(79, 195, 247, 0.8);
    border-color: #4FC3F7;
    transform: scale(1.1);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1.2rem 2.5rem;
    border-radius: 16px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: none;
    cursor: pointer;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    color: var(--white);
    box-shadow: 0 8px 24px rgba(79, 195, 247, 0.3);
}

.btn-primary:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 40px rgba(79, 195, 247, 0.4);
    background: linear-gradient(135deg, #29B6F6 0%, #0277BD 100%);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: 0 4px 16px rgba(79, 195, 247, 0.1);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(79, 195, 247, 0.3);
}

/* Carousel Animation Effects */
.carousel-slide {
    animation: slideIn 1s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(100%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

.slide-content {
    animation: contentFadeIn 1.2s ease-out 0.3s both;
}

@keyframes contentFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-scroll {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    color: var(--primary-color);
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Stats Section */
.stats {
    padding: 5rem 0;
    background: var(--white);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.stat-item {
    text-align: center;
    padding: 3rem 2rem;
    border-radius: 24px;
    background: #FFFFFF;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.04);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stat-item:hover::before {
    transform: scaleX(1);
}

.stat-item:hover {
    transform: translateY(-12px);
    box-shadow: 0 20px 60px rgba(79, 195, 247, 0.15);
}

.stat-icon {
    width: 90px;
    height: 90px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    border-radius: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 2.2rem;
    box-shadow: 0 8px 24px rgba(79, 195, 247, 0.3);
    transition: all 0.3s ease;
}

.stat-item:hover .stat-icon {
    transform: scale(1.1) rotate(-5deg);
    box-shadow: 0 12px 32px rgba(79, 195, 247, 0.4);
}

.stat-number {
    font-size: 3rem;
    font-weight: 900;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    color: var(--text-light);
    font-weight: 500;
}

/* About Section */
.about-section {
    padding: 8rem 0;
    background: #FFFFFF;
    position: relative;
    overflow: hidden;
}

.about-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
    align-items: center;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, rgba(79, 195, 247, 0.1) 0%, rgba(79, 195, 247, 0.05) 100%);
    color: var(--primary-color);
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(79, 195, 247, 0.2);
}

.about-title {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1.2;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.highlight-text {
    background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.about-description {
    font-size: 1.2rem;
    color: var(--text-light);
    line-height: 1.8;
    margin-bottom: 3rem;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: #F8FAFB;
    border-radius: 16px;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: #FFFFFF;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
    transform: translateX(-5px);
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.feature-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.3rem;
}

.feature-content p {
    color: var(--text-light);
    font-size: 0.95rem;
}

.about-cta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

/* About Visual Section */
.about-visual {
    position: relative;
}

.about-image-container {
    position: relative;
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.main-image {
    width: 100%;
    height: 500px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.about-image-container:hover .main-image {
    transform: scale(1.05);
}

.floating-card {
    position: absolute;
    bottom: -20px;
    right: -20px;
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    min-width: 200px;
}

.card-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.card-content h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.2rem;
}

.card-content p {
    color: var(--text-light);
    font-size: 0.9rem;
}

.decorative-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.element {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(79, 195, 247, 0.1) 0%, rgba(79, 195, 247, 0.05) 100%);
}

.element-1 {
    width: 80px;
    height: 80px;
    top: -40px;
    left: -40px;
    animation: float 6s ease-in-out infinite;
}

.element-2 {
    width: 60px;
    height: 60px;
    top: 50%;
    right: -30px;
    animation: float 4s ease-in-out infinite reverse;
}

.element-3 {
    width: 100px;
    height: 100px;
    bottom: -50px;
    left: 20%;
    animation: float 8s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Services Section */
.services-section {
    padding: 8rem 0;
    background: #F8FAFB;
    position: relative;
}

.services-header {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-bottom: 6rem;
}

.services-title {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1.2;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.services-description {
    font-size: 1.2rem;
    color: var(--text-light);
    line-height: 1.8;
}

.services-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 24px;
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.08);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.service-card {
    position: relative;
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(0, 0, 0, 0.04);
    height: 500px;
    display: flex;
    flex-direction: column;
}

.service-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 60%;
    z-index: 1;
}

.service-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-card:hover .service-background img {
    transform: scale(1.1);
}

.service-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 60%;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.7) 0%,
        rgba(79, 195, 247, 0.3) 50%,
        rgba(0, 0, 0, 0.5) 100%
    );
    z-index: 2;
}

.service-content {
    position: relative;
    z-index: 3;
    padding: 2rem;
    background: white;
    margin-top: 60%;
    height: 40%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.service-card.featured {
    border: 2px solid #FFD700;
    transform: scale(1.02);
}

.service-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 4;
}

.service-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 6px 20px rgba(79, 195, 247, 0.3);
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 28px rgba(79, 195, 247, 0.4);
}

.service-card h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.service-card p {
    color: var(--text-light);
    line-height: 1.7;
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

.service-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.service-features span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-light);
}

.service-features i {
    color: #10B981;
    font-size: 0.8rem;
}

.service-cta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #F8FAFB 0%, #E3F2FD 100%);
    border-radius: 12px;
    text-decoration: none;
    color: var(--primary-color);
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: auto;
}

.service-cta:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(-5px);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(79, 195, 247, 0.15);
}

.service-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.8rem;
    box-shadow: 0 8px 24px rgba(79, 195, 247, 0.3);
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 12px 32px rgba(79, 195, 247, 0.4);
}

/* Services Bottom CTA */
.services-bottom-cta {
    background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    padding: 3rem;
    border-radius: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
    margin-top: 4rem;
}

.cta-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.cta-content p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.btn-large {
    padding: 1.5rem 3rem;
    font-size: 1.1rem;
    background: white;
    color: var(--primary-color);
}

.btn-large:hover {
    background: #F8FAFB;
    transform: translateY(-3px);
}

/* Sectors Section */
.sectors {
    padding: 5rem 0;
    background: #F8FAFB;
    position: relative;
}

.sectors::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%234FC3F7" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
}

.sectors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.sector-card {
    background: var(--white);
    padding: 2.5rem 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.04);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
}

.sector-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.sector-card:hover::after {
    transform: scaleX(1);
}

.sector-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(79, 195, 247, 0.12);
}

.sector-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.8rem;
    margin: 0 auto 1.5rem;
    box-shadow: 0 6px 20px rgba(79, 195, 247, 0.25);
    transition: all 0.3s ease;
}

.sector-card:hover .sector-icon {
    transform: scale(1.15);
    box-shadow: 0 8px 28px rgba(79, 195, 247, 0.35);
}

.sector-card h3 {
    font-size: 1.1rem;
    color: var(--text-dark);
    font-weight: 600;
}

/* Investment Section */
.investment {
    padding: 5rem 0;
    background: var(--white);
}

.investment-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.contact-form {
    background: #FFFFFF;
    padding: 3rem;
    border-radius: 24px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(0, 0, 0, 0.04);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
    font-weight: 600;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1.2rem 1.5rem;
    border: 2px solid rgba(0, 0, 0, 0.08);
    border-radius: 16px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #FAFBFC;
    font-family: 'Cairo', sans-serif;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(79, 195, 247, 0.1);
    background: var(--white);
    transform: translateY(-2px);
}

.investment-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: var(--shadow);
}

/* Opportunities Section */
.opportunities {
    padding: 5rem 0;
    background: #FFFFFF;
    position: relative;
}

.opportunities::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(79, 195, 247, 0.01) 50%, transparent 100%);
    pointer-events: none;
}

.opportunities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.opportunity-card {
    background: var(--white);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
}

.opportunity-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(79, 195, 247, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.opportunity-card:hover::before {
    opacity: 1;
}

.opportunity-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 24px 64px rgba(79, 195, 247, 0.15);
}

.opportunity-image {
    height: 200px;
    overflow: hidden;
}

.opportunity-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.opportunity-card:hover .opportunity-image img {
    transform: scale(1.1);
}

.opportunity-content {
    padding: 2rem;
}

.opportunity-content h3 {
    font-size: 1.3rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
    line-height: 1.4;
}

.opportunity-content p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* Testimonials Section */
.testimonials {
    padding: 5rem 0;
    background: var(--white);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: #FFFFFF;
    padding: 2.5rem;
    border-radius: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.04);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    font-size: 4rem;
    color: rgba(79, 195, 247, 0.1);
    font-family: serif;
    line-height: 1;
}

.testimonial-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 48px rgba(79, 195, 247, 0.12);
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.stars {
    color: #FFD700;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.testimonial-content p {
    color: var(--text-light);
    font-style: italic;
    line-height: 1.7;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.author-info h4 {
    color: var(--text-dark);
    margin-bottom: 0.2rem;
}

.author-info span {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Contact Section */
.contact {
    padding: 5rem 0;
    background: #F8FAFB;
    position: relative;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(79, 195, 247, 0.02) 50%, transparent 100%);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    background: var(--white);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(79, 195, 247, 0.1);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.5rem;
}

.contact-details h4 {
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: var(--text-light);
    margin: 0;
}

.contact-form-container {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: var(--shadow);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo {
    height: 60px;
    margin-bottom: 1rem;
}

.footer-section p {
    color: #B0BEC5;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--white);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #B0BEC5;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-dark);
    transform: translateY(-3px);
}

.footer-article {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #37474F;
}

.footer-article h5 {
    color: var(--white);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.footer-article span {
    color: #78909C;
    font-size: 0.8rem;
}

.footer-bottom {
    border-top: 1px solid #37474F;
    padding-top: 1rem;
    text-align: center;
    color: #B0BEC5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .slide-content {
        text-align: center;
        align-items: center;
        max-width: 90%;
        margin: 0 auto;
    }

    .slide-title {
        font-size: 2.5rem;
    }

    .slide-subtitle {
        font-size: 1.1rem;
    }

    .carousel-nav {
        padding: 0 1rem;
    }

    .carousel-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .about-wrapper,
    .services-header,
    .investment-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .about-title,
    .services-title {
        font-size: 2.2rem;
    }

    .floating-card {
        position: static;
        margin-top: 2rem;
    }

    .services-bottom-cta {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }

    .services-grid,
    .opportunities-grid {
        grid-template-columns: 1fr;
    }

    .sectors-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .form-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .slide-title {
        font-size: 2rem;
    }

    .slide-subtitle {
        font-size: 1rem;
    }

    .carousel-indicators {
        bottom: 1rem;
    }

    .indicator {
        width: 10px;
        height: 10px;
    }

    .sectors-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
    }
}
