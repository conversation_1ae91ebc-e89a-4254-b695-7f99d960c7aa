/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #4FC3F7;
    --primary-dark: #29B6F6;
    --primary-light: #81D4FA;
    --secondary-color: #E1F5FE;
    --accent-color: #0277BD;
    --text-dark: #263238;
    --text-light: #546E7A;
    --white: #FFFFFF;
    --gradient: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    --shadow: 0 10px 30px rgba(79, 195, 247, 0.2);
    --shadow-hover: 0 15px 40px rgba(79, 195, 247, 0.3);
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    height: 50px;
    width: auto;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    background: #FFFFFF;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(79, 195, 247, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(79, 195, 247, 0.03) 0%, transparent 50%);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 900;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: var(--text-dark);
}

.highlight {
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 2.5rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1.2rem 2.5rem;
    border-radius: 16px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: none;
    cursor: pointer;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    color: var(--white);
    box-shadow: 0 8px 24px rgba(79, 195, 247, 0.3);
}

.btn-primary:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 40px rgba(79, 195, 247, 0.4);
    background: linear-gradient(135deg, #29B6F6 0%, #0277BD 100%);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: 0 4px 16px rgba(79, 195, 247, 0.1);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-4px);
    box-shadow: 0 12px 32px rgba(79, 195, 247, 0.3);
}

.hero-image {
    position: relative;
}

.hero-image img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.hero-image:hover img {
    transform: scale(1.05);
}

.hero-scroll {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    color: var(--primary-color);
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Stats Section */
.stats {
    padding: 5rem 0;
    background: var(--white);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.stat-item {
    text-align: center;
    padding: 3rem 2rem;
    border-radius: 24px;
    background: #FFFFFF;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.04);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stat-item:hover::before {
    transform: scaleX(1);
}

.stat-item:hover {
    transform: translateY(-12px);
    box-shadow: 0 20px 60px rgba(79, 195, 247, 0.15);
}

.stat-icon {
    width: 90px;
    height: 90px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    border-radius: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 2.2rem;
    box-shadow: 0 8px 24px rgba(79, 195, 247, 0.3);
    transition: all 0.3s ease;
}

.stat-item:hover .stat-icon {
    transform: scale(1.1) rotate(-5deg);
    box-shadow: 0 12px 32px rgba(79, 195, 247, 0.4);
}

.stat-number {
    font-size: 3rem;
    font-weight: 900;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    color: var(--text-light);
    font-weight: 500;
}

/* About Section */
.about {
    padding: 5rem 0;
    background: #FFFFFF;
    position: relative;
}

.about::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 49%, rgba(79, 195, 247, 0.02) 50%, transparent 51%);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(79, 195, 247, 0.3);
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h3 {
    font-size: 2rem;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.about-text p {
    font-size: 1.1rem;
    color: var(--text-light);
    line-height: 1.8;
    margin-bottom: 2rem;
}

.features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.feature i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.about-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: var(--shadow);
}

/* Services Section */
.services {
    padding: 5rem 0;
    background: var(--white);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.service-card {
    background: #FFFFFF;
    padding: 3rem 2.5rem;
    border-radius: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.04);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-12px);
    box-shadow: 0 20px 60px rgba(79, 195, 247, 0.15);
    border-color: rgba(79, 195, 247, 0.2);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 24px rgba(79, 195, 247, 0.3);
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 12px 32px rgba(79, 195, 247, 0.4);
}

.service-card h3 {
    font-size: 1.5rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.service-card p {
    color: var(--text-light);
    line-height: 1.7;
}

/* Sectors Section */
.sectors {
    padding: 5rem 0;
    background: #F8FAFB;
    position: relative;
}

.sectors::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%234FC3F7" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
}

.sectors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.sector-card {
    background: var(--white);
    padding: 2.5rem 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.04);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
}

.sector-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.sector-card:hover::after {
    transform: scaleX(1);
}

.sector-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(79, 195, 247, 0.12);
}

.sector-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.8rem;
    margin: 0 auto 1.5rem;
    box-shadow: 0 6px 20px rgba(79, 195, 247, 0.25);
    transition: all 0.3s ease;
}

.sector-card:hover .sector-icon {
    transform: scale(1.15);
    box-shadow: 0 8px 28px rgba(79, 195, 247, 0.35);
}

.sector-card h3 {
    font-size: 1.1rem;
    color: var(--text-dark);
    font-weight: 600;
}

/* Investment Section */
.investment {
    padding: 5rem 0;
    background: var(--white);
}

.investment-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.contact-form {
    background: #FFFFFF;
    padding: 3rem;
    border-radius: 24px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(0, 0, 0, 0.04);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
    font-weight: 600;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid rgba(79, 195, 247, 0.2);
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--white);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.1);
}

.investment-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: var(--shadow);
}

/* Opportunities Section */
.opportunities {
    padding: 5rem 0;
    background: #FFFFFF;
    position: relative;
}

.opportunities::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(79, 195, 247, 0.01) 50%, transparent 100%);
    pointer-events: none;
}

.opportunities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.opportunity-card {
    background: var(--white);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
}

.opportunity-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(79, 195, 247, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.opportunity-card:hover::before {
    opacity: 1;
}

.opportunity-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 24px 64px rgba(79, 195, 247, 0.15);
}

.opportunity-image {
    height: 200px;
    overflow: hidden;
}

.opportunity-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.opportunity-card:hover .opportunity-image img {
    transform: scale(1.1);
}

.opportunity-content {
    padding: 2rem;
}

.opportunity-content h3 {
    font-size: 1.3rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
    line-height: 1.4;
}

.opportunity-content p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* Testimonials Section */
.testimonials {
    padding: 5rem 0;
    background: var(--white);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: #FFFFFF;
    padding: 2.5rem;
    border-radius: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.04);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    font-size: 4rem;
    color: rgba(79, 195, 247, 0.1);
    font-family: serif;
    line-height: 1;
}

.testimonial-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 48px rgba(79, 195, 247, 0.12);
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.stars {
    color: #FFD700;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.testimonial-content p {
    color: var(--text-light);
    font-style: italic;
    line-height: 1.7;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.author-info h4 {
    color: var(--text-dark);
    margin-bottom: 0.2rem;
}

.author-info span {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Contact Section */
.contact {
    padding: 5rem 0;
    background: #F8FAFB;
    position: relative;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(79, 195, 247, 0.02) 50%, transparent 100%);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    background: var(--white);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(79, 195, 247, 0.1);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.5rem;
}

.contact-details h4 {
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: var(--text-light);
    margin: 0;
}

.contact-form-container {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: var(--shadow);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo {
    height: 60px;
    margin-bottom: 1rem;
}

.footer-section p {
    color: #B0BEC5;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--white);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #B0BEC5;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-dark);
    transform: translateY(-3px);
}

.footer-article {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #37474F;
}

.footer-article h5 {
    color: var(--white);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.footer-article span {
    color: #78909C;
    font-size: 0.8rem;
}

.footer-bottom {
    border-top: 1px solid #37474F;
    padding-top: 1rem;
    text-align: center;
    color: #B0BEC5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .about-content,
    .investment-content,
    .contact-content {
        grid-template-columns: 1fr;
    }

    .services-grid,
    .opportunities-grid {
        grid-template-columns: 1fr;
    }

    .sectors-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .form-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .sectors-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
    }
}
