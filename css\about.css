
        
        /* Hero Section */
        .about-hero {
            background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('/images/muslim-guy-checking-charts-data.jpg');
            background-size: cover;
            background-position: center;
            height: 60vh;
            min-height: 500px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            position: relative;
        }
        
        .about-hero-content {
            max-width: 800px;
            padding: 0 20px;
        }
        
        .about-hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
            animation: fadeInDown 1s ease;
        }
        
        .about-hero p {
            font-size: 1.25rem;
            margin-bottom: 30px;
            text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.5);
            animation: fadeInUp 1s ease 0.3s both;
        }
        
        /* About Section */
        .about-section {
            padding: 80px 0;
            background-color: #ffffff;
        }
        
        .about-img {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .about-img:hover {
            transform: translateY(-10px);
        }
        
        .about-content {
            padding-right: 30px;
        }
        
        .section-title {
            position: relative;
            margin-bottom: 30px;
            color: var(--secondary-color);
            font-weight: 700;
        }
        
        .section-title:after {
            content: '';
            position: absolute;
            bottom: -15px;
            right: 0;
            width: 80px;
            height: 3px;
            background: var(--primary-color);
            animation: lineGrow 1s ease-out;
        }
        
        @keyframes lineGrow {
            from { width: 0; }
            to { width: 80px; }
        }
        
        /* Features Section */
        .features-section {
            background-color: var(--light-color);
            padding: 80px 0;
        }
        
        .feature-box {
            background-color: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
        .feature-box:before {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 100%;
            height: 0;
            
            z-index: -1;
            transition: all 0.4s ease;
        }
        
        .feature-box:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
            color: rgb(0, 0, 0);
        }
        
        
        
        .feature-icon {
            font-size: 2.8rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        /* Mission Section */
        /* Mission Section */
.mission-section {
    position: relative;
    color: white;
    padding: 100px 0;
}

.mission-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('/images/2148252130.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    z-index: -1;
}

.mission-item {
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    height: 100%;
}

.mission-item:hover {
    transform: translateY(-10px);
    background-color: rgba(65, 174, 217, 0.2);
    border-color: var(--primary-color);
}

.mission-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}
        
        /* Stats Section */
        .stats-section {
            padding: 80px 0;
            background-color: white;
        }
        
        .stat-box {
            text-align: center;
            padding: 30px;
            border-radius: 15px;
            background-color: var(--light-color);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .stat-box:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(65, 174, 217, 0.15);
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        /* Testimonials */
        .testimonial-box {
            border: none;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            margin: 0 15px;
            height: 100%;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            position: relative;
            padding: 20px;
        }
        
        .testimonial-box:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .testimonial-box:before {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 100%;
            height: 5px;
            background: var(--primary-color);
            transform: scaleX(0);
            transform-origin: right;
            transition: transform 0.3s ease;
        }
        
        .testimonial-box:hover:before {
            transform: scaleX(1);
            transform-origin: left;
        }
        
        .client-img-container {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid var(--primary-color);
            padding: 3px;
            transition: all 0.3s ease;
            margin: 0 auto 20px;
        }
        
        .testimonial-box:hover .client-img-container {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(65, 174, 217, 0.3);
        }
        
        .client-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .testimonial-box:hover .client-img {
            transform: scale(1.05);
        }
        
        .rating {
            color: #ffc107;
            font-size: 1rem;
            margin-bottom: 10px;
        }
        
        /* Buttons */
        .btn {
            font-weight: 500;
            padding: 0.6rem 1.5rem;
            border-radius: 50px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
        .btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            z-index: -1;
        }
        
        .btn:hover:before {
            width: 100%;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #3a9cc8;
            border-color: #3a9cc8;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(65, 174, 217, 0.3);
        }
        
        .btn-outline-light {
            border-color: white;
            color: white;
        }
        
        .btn-outline-light:hover {
            background-color: white;
            color: var(--primary-color);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(255, 255, 255, 0.3);
        }
        
        /* Animation Classes */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }
        
        .animate-on-scroll.animated {
            opacity: 1;
            transform: translateY(0);
        }
        
        .delay-1 {
            transition-delay: 0.2s;
        }
        
        .delay-2 {
            transition-delay: 0.4s;
        }
        
        .delay-3 {
            transition-delay: 0.6s;
        }
        
        /* Responsive Adjustments */
        @media (max-width: 992px) {
            .about-content {
                padding-right: 0;
                margin-bottom: 40px;
            }
            
            .about-hero h1 {
                font-size: 2.5rem;
            }
        }
        
        @media (max-width: 768px) {
            .about-hero {
                height: 50vh;
                min-height: 400px;
            }
            
            .about-hero h1 {
                font-size: 2rem;
            }
            
            .about-hero p {
                font-size: 1rem;
            }
        }
        
        @media (max-width: 576px) {
            .about-hero {
                height: 40vh;
                min-height: 300px;
            }
        }