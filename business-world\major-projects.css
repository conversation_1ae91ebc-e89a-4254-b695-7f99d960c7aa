/* Root Variables */
:root {
    --primary-color: #4FC3F7;
    --secondary-color: #29B6F6;
    --accent-color: #81C784;
    --success-color: #66BB6A;
    --text-dark: #2C3E50;
    --text-light: #7F8C8D;
    --white: #FFFFFF;
    --light-bg: #F8FAFB;
    --gradient: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
    --shadow: 0 4px 20px rgba(79, 195, 247, 0.1);
    --shadow-hover: 0 8px 30px rgba(79, 195, 247, 0.2);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background: var(--white);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.highlight {
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    border-bottom: 1px solid rgba(79, 195, 247, 0.1);
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand .logo {
    height: 50px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 600;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: var(--primary-color);
}

.nav-menu a.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--gradient);
    border-radius: 1px;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero-section {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(79, 195, 247, 0.8) 0%,
        rgba(41, 182, 246, 0.6) 100%
    );
    z-index: -1;
}

.hero-content {
    text-align: center;
    color: white;
    max-width: 800px;
    animation: fadeInUp 1s ease;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    backdrop-filter: blur(10px);
    margin-bottom: 2rem;
    font-weight: 600;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.9;
    line-height: 1.8;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
}

.hero-scroll {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

/* Filter Section */
.filter-section {
    padding: 4rem 0;
    background: var(--light-bg);
}

.filter-wrapper {
    text-align: center;
}

.filter-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 3rem;
}

.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
}

.filter-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: var(--white);
    border: 1px solid rgba(79, 195, 247, 0.2);
    border-radius: 12px;
    color: var(--text-dark);
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    box-shadow: 0 2px 10px rgba(79, 195, 247, 0.05);
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--gradient);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(79, 195, 247, 0.2);
}

.filter-btn i {
    font-size: 1rem;
}

/* Projects Section */
.projects-section {
    padding: 5rem 0;
    background: var(--white);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.project-card {
    background: var(--white);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(79, 195, 247, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(79, 195, 247, 0.08);
    opacity: 1;
    transform: scale(1);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.project-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(79, 195, 247, 0.15);
    border-color: rgba(79, 195, 247, 0.2);
}

.project-card.hidden {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

.project-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.05);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(79, 195, 247, 0.7) 0%,
        rgba(41, 182, 246, 0.5) 100%
    );
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    padding: 1rem;
}

.project-category {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    background: rgba(255, 255, 255, 0.95);
    color: var(--primary-color);
    padding: 0.4rem 0.8rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.project-content {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.project-content h3 {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.8rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.project-content p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.project-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.project-features span {
    display: flex;
    align-items: center;
    gap: 0.6rem;
    font-size: 0.8rem;
    color: var(--text-dark);
    font-weight: 500;
}

.project-features i {
    color: var(--success-color);
    font-size: 0.9rem;
    width: 12px;
}

.project-footer {
    border-top: 1px solid rgba(79, 195, 247, 0.08);
    padding: 1rem 1.5rem;
    margin-top: auto;
    background: rgba(248, 250, 251, 0.5);
}

.project-investment {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.investment-label {
    font-size: 0.8rem;
    color: var(--text-light);
    font-weight: 500;
}

.investment-value {
    font-size: 0.9rem;
    color: var(--primary-color);
    font-weight: 700;
}

.project-actions {
    display: flex;
    gap: 0.8rem;
}

.btn-primary,
.btn-outline {
    flex: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    padding: 0.6rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    text-align: center;
}

.btn-primary {
    background: var(--gradient);
    color: white;
    box-shadow: 0 2px 8px rgba(79, 195, 247, 0.2);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 195, 247, 0.3);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border-color: rgba(79, 195, 247, 0.3);
}

.btn-outline:hover {
    background: rgba(79, 195, 247, 0.1);
    border-color: var(--primary-color);
}

.btn-primary i,
.btn-outline i {
    font-size: 0.75rem;
}

/* Benefits Section */
.benefits-section {
    padding: 5rem 0;
    background: var(--light-bg);
}

.benefits-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(79, 195, 247, 0.1);
    color: var(--primary-color);
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.benefits-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.benefits-description {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.7;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.benefit-card {
    background: var(--white);
    padding: 2.5rem 2rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(79, 195, 247, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(79, 195, 247, 0.08);
}

.benefit-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(79, 195, 247, 0.15);
}

.benefit-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.benefit-card h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.benefit-card p {
    color: var(--text-light);
    line-height: 1.7;
}

/* CTA Section */
.cta-section {
    padding: 5rem 0;
    background: var(--gradient);
    color: white;
    text-align: center;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-text p {
    font-size: 1.1rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    line-height: 1.7;
}

.cta-actions {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
}

.btn-whatsapp {
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem 2rem;
    background: #25D366;
    color: white;
    text-decoration: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
}

.btn-whatsapp:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
}

.cta-actions .btn-primary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 1rem 2rem;
    font-size: 1rem;
    gap: 0.8rem;
}

.cta-actions .btn-primary:hover {
    background: white;
    color: var(--primary-color);
    border-color: white;
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1.5rem;
    color: white;
}

.footer-section p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.footer-logo img {
    height: 40px;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(79, 195, 247, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.8rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: rgba(255, 255, 255, 0.8);
}

.contact-item i {
    color: var(--primary-color);
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .projects-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .hero-title {
        font-size: 3rem;
    }

    .hero-stats {
        gap: 2rem;
    }

    .filter-buttons {
        gap: 0.8rem;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1.5rem;
    }

    .projects-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .filter-buttons {
        flex-direction: column;
        align-items: center;
        gap: 0.8rem;
    }

    .filter-btn {
        width: 200px;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .cta-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .filter-title {
        font-size: 2rem;
    }

    .benefits-title {
        font-size: 2rem;
    }

    .cta-text h2 {
        font-size: 2rem;
    }

    .project-content {
        padding: 1.2rem;
    }

    .project-content h3 {
        font-size: 1rem;
    }

    .project-content p {
        font-size: 0.85rem;
    }

    .project-actions {
        flex-direction: column;
        gap: 0.6rem;
    }

    .btn-primary,
    .btn-outline {
        padding: 0.7rem 1rem;
        font-size: 0.8rem;
    }

    .filter-btn {
        padding: 0.8rem 1rem;
        font-size: 0.85rem;
        width: 180px;
    }

    .benefit-card {
        padding: 2rem 1.5rem;
    }

    .benefit-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}
